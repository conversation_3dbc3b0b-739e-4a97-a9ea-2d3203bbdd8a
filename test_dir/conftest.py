import copy
import json

import pytest
from weeeTest import log, jmespath
from weeeTest.testdata.common.mysql.ext.mysqlUtil import MysqlUtil

from test_data.ec.simple.common import Header
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.get_root_dir import get_project_dir



# 自动化账号管理：
# daily总账号：<EMAIL> /A1234567    > user_id:b2b:15257267, weee:12491964,masgusto:15257259
# 监控账总账号：<EMAIL> /A1234567  > user_id:b2b:15257231, weee:13537530,masgusto:15257246
# 监控账号数据：<EMAIL> /A1234567  -- 目前没用
# 监控账号不同region下单：<EMAIL> /A1234567  > user_id:weee:15012459
# mkpl 账号：<EMAIL> /A1234567 (密码不对) > user_id:
# mkpl 账号：<EMAIL> /A1234567 (密码不对)  --chao  > user_id:
# 社区账号：<EMAIL> /A1234567 (密码不对) --jiufen > user_id:
# 订单账号：<EMAIL> /A1234567  --zhuli > user_id: 15257292
# 订单账号：<EMAIL>/1234qwer  --zhuli  > user_id:b2b: 15286276   weee:9158213,masgusto:15286288
#

@pytest.fixture(scope="function")
def empty_cart(request, ec_login_header):
    try:
        header = request.param[0] if request.param is not None else ec_login_header
    except Exception as e:
        log.info("fixture没有传递参数 " + str(e))
        header = ec_login_header
    my_porder = QueryPreOrder().query_preorder_v5(headers=header, cart_domain='grocery')
    sections = my_porder["object"]["sections"]
    if len(sections) > 0:
        common_product_ids = [[item['product_id'], item.get('refer_type', 'normal')] for section in sections for item in
                              section.get('items') if
                              len(section.get('items')) > 0]
        try:
            activity_product_ids = [[item.get('product_id', ""), item.get('refer_type', 'normal')] for section in
                                    sections for activity_info in
                                    section.get('activity_info') for item in activity_info.get('items', []) if
                                    len(section.get('activity_info')) > 0 and len(activity_info.get('items')) > 0]
        except Exception as e:
            print("集合中不存在activity_product_ids" + str(e))
            activity_product_ids = []

        all_product_ids = common_product_ids + activity_product_ids
        for product_id in all_product_ids:
            print(f"deleting product, id is {product_id}")
            UpdatePreOrderLine().porder_items_v3(headers=header, product_id=product_id[0],
                                                 quantity=0, source="s_ja|app_restaurant_detail",
                                                 refer_type=product_id[0])


    else:
        print("此购物车不存在商品")


@pytest.fixture(scope='session')
def monitor_header(request):
    monitor_header = Header.login_header(email='<EMAIL>', password='A1234567')
    return monitor_header


@pytest.fixture(scope='session')
def region_monitor_header(request):
    region_monitor_header = Header.login_header(email='<EMAIL>', password='A1234567')
    return region_monitor_header


@pytest.fixture(scope='session')
def b2b_header(request):
    b2b_header = Header.login_header(email='<EMAIL>', password='A1234567')
    return b2b_header


@pytest.fixture(scope='session')
def pc_header():
    with open(get_project_dir() + "/test_data/autotest_token.json", "r", encoding='utf-8') as f:
        pc_header = json.load(f)
    pc_header['platform'] = 'pc'
    pc_header[
        'user-agent'] = r'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36'
    return pc_header


# @pytest.fixture(scope='session')
# def ec_login_header_origin():
#     autotest_header = Header.login_header()
#     with open(get_project_dir() + "/test_data/autotest_token.json", "w", encoding='utf-8') as f:
#         f.write(json.dumps(autotest_header))

@pytest.fixture(scope='class')
def ec_login_header():
    with open(get_project_dir() + "/test_data/autotest_token.json", "r", encoding='utf-8') as f:
        autotest_header = json.load(f)
    return autotest_header


@pytest.fixture(scope='session')
def ec_anony_header():
    return Header.anony_header()


@pytest.fixture(scope='session')
def ec_signup_header():
    return Header.signup_header()


# 购物车结算专用

# @pytest.fixture(scope='session')
# def ec_zhuli_header_origin():
#     zhuli_header = Header.login_header(email='<EMAIL>', password='1234qwer')
#     with open(get_project_dir() + "/test_data/lizhu2_token.json", "w", encoding='utf-8') as f:
#         f.write(json.dumps(zhuli_header))
@pytest.fixture(scope='class')
def ec_zhuli_header():
    with open(get_project_dir() + "/test_data/lizhu2_token.json", 'r', encoding='utf-8') as f:
        zhuli_header = json.load(f)
    return zhuli_header


# af专用
@pytest.fixture(scope='session')
def ec_af_header(ec_login_header):
    return ec_login_header


# 社区专用
@pytest.fixture(scope='session')
def ec_jiufen_header():
    return Header.login_header(email='<EMAIL>', password='wo466125')


# MKPL专用 同时返回当前账号第一个地址的zipcode sale_org date
@pytest.fixture(scope='session')
def ec_mkpl_header():
    header = Header.login_header(email='<EMAIL>', password='qwert12345')
    addr_lst = QueryUserAddressList().address_list(header)
    # 取地址簿第一个地址，headers zipcode delivery data
    addr_id = jmespath(addr_lst, 'object[0].address_id')
    zipcode = jmespath(addr_lst, 'object[0].addr_zipcode')
    header['Zipcode'] = zipcode
    header['Weee-Zipcode'] = zipcode
    header['Lang'] = 'en'
    header['Weee-Store'] = 'cn'
    simple_res = QuerySimplePreOrder().query_simple_preorder_v1(header)
    sales_org_id = jmespath(simple_res, "object.sales_org_id")
    date = jmespath(simple_res, "object.delivery_date")
    return {"addr_header": header, "zipcode": zipcode, "sales_org_id": sales_org_id,
            "date": date}


# MKPL 拼单
@pytest.fixture(scope='session')
def ec_mkpl_guest_header():
    return Header.login_header(email='<EMAIL>', password='qwert12345')


@pytest.fixture(scope='session')
def mkpl_xiaojia_header(ec_login_header):
    return ec_login_header


@pytest.fixture(scope='session')
def ec_charlie_header():
    return Header.login_header(email='<EMAIL>', password='test1234')


@pytest.fixture(scope='session')
def tb1_conn():
    try:
        with MysqlUtil(host="weee.db.tb1.sayweee.net", user="weee_auto_test", password="&!w1vgEJHW6fsTEb",
                       db="autotest_result",
                       port=3306, return_type=None) as conn:
            yield conn
    except Exception as e:
        log.error("连接数据库失败" + str(e))
        raise e
