"""
<AUTHOR>  suqin
@Version        :  V1.0.0
------------------------------------
@File           :  RemoveAllProductsInCart.py.py
@Description    :
@CreateTime     :  2023/8/12 17:40
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/12 17:40
"""
import datetime
import json

import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine


class CategoryCheck(weeeTest.TestCase):
    def category_add_cart_check(self, headers, filter_sub_category, filters: dict = None,
                                sort: str = "recommend", default_sort_key: str = None, cuisine: str = None,
                                filter_ethnic: str = None,
                                lang: str = "en", zipcode: int = 98011,
                                sales_org_id: int = 4, limit: int = 10,
                                date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
                                    '%Y-%m-%d')):
        """分类加购公共case"""
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        # 切换普通分类
        SearchByCatalogueContent().search_by_catalogue_content(headers=headers,
                                                               filter_sub_category=filter_sub_category,
                                                               filters=filters,
                                                               sort=sort, default_sort_key=default_sort_key,
                                                               cuisine=cuisine,
                                                               filter_ethnic=filter_ethnic,
                                                               lang=lang, zipcode=zipcode,
                                                               limit=limit,
                                                               date=date)
        assert self.response["result"] is True
        # 获取分类下的第一个商品
        for product in self.response["object"]["contents"]:
            # 可售商品
            if product["type"] == "product" and product["sold_status"] == "available":
                product_id = product["data"]["id"]
                if product["data"]["biz_type"] is None:
                    refer_type = "normal"
                else:
                    refer_type = product["data"]["biz_type"]
                # 加购特殊分类下的生鲜商品
                UpdatePreOrderLine().porder_items_v3(headers=headers, product_id=product_id,
                                                     date=porder["delivery_pickup_date"],
                                                     quantity=product["data"]["min_order_quantity"],
                                                     item_type=product["data"]["item_type"],
                                                     is_pantry=product["data"]["is_pantry"],
                                                     is_mkpl=product["data"]["is_mkpl"],
                                                     refer_type=refer_type,
                                                     source="mweb_category-" + str(filter_sub_category) + "-all")

                # 判断加购成功
                assert self.response["result"] is True
                # 判断加入购物车成功
                # CheckProductsAddedToCartSuccess().check_product_add_to_cart_success(headers=headers,
                #                                                                     cart_domain="grocery",
                #                                                                     product_id=product_id)
            break

    def category_product(self, headers, deal_date, filter_sub_category, zipcode: int = 98011,
                         filters: dict | str = json.dumps({"delivery_type": "delivery_type_local"}),
                         ):
        # 返回公共分类数据
        # 1. 通过分类接口获取零食商品
        normal_category = SearchByCatalogueContent().search_by_catalogue_content(headers=headers, zipcode=zipcode,
                                                                                 date=deal_date,sort="recommend",
                                                                                 filter_sub_category=filter_sub_category,
                                                                                 filters=filters
                                                                                 )
        assert normal_category["object"]["total_count"] > 0, f"分类{normal_category}返回没有数据，请确认"
        return normal_category




if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
