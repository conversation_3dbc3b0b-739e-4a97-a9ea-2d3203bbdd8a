# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  <PERSON><PERSON><PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @Description    :
# @CreateTime     :  2023/7/18
# @Software       :  PyCharm
# ------------------------------------
# """

import weeeTest

from test_dir.api.ec.ec_social.event.event import Event


class TestUpdateEventInfo(weeeTest.TestCase):
    @weeeTest.mark.list('Social', 'tb1', 'dev')
    def test_update_event_info(self,ec_jiufen_header):
        """更新event信息"""
        Event().update_event_info(id='2', headers=ec_jiufen_header)
        case_result = self.response
        assert case_result is not None

