import weeeTest

from test_dir.api.ec.ec_item.recommend.api_bought import GetBoughtProducts



class TestGetBoughtProductsSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_bought_products(self, ec_login_header):
        """get_bought_products"""
        GetBoughtProducts().get_bought_products(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_bought_products_list(self, ec_login_header):
        """get_bought_products_list"""
        GetBoughtProducts().get_bought_products_list(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_bought_products_cart(self, ec_login_header):
        """get_bought_products_cart"""
        GetBoughtProducts().get_bought_products_cart(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_bought_products_social(self, ec_login_header):
        """get_bought_products_social"""
        GetBoughtProducts().get_bought_products_social(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True

