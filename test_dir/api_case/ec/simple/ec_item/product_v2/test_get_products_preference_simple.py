import weeeTest

from test_dir.api.ec.ec_item.product_v2.get_products_preference import GetProductsPreference



class TestGetProductsPreferenceSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'fail_product', 'dev')
    def test_get_products_preference(self, ec_login_header):
        """test_get_products_preference"""
        GetProductsPreference().get_products_recommend(headers=ec_login_header, product_id=10395)
        # 断言
        assert self.response["result"] is True
        assert self.response["object"] is not None
