# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest

from test_dir.api.ec.ec_so.order_query.review_post_product import ReviewPostProduct


class TestReviewPostProductSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'fail_product')
    def test_review_post_product(self, ec_login_header):
        """ # test_query_order_user_product """
        # 'Request Parameter is invalid, please check.' 请排查
        ReviewPostProduct().review_post_product(headers=ec_login_header)
        print(ReviewPostProduct().review_post_product(headers=ec_login_header))

        # 断言
        assert self.response["result"] is True




if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
