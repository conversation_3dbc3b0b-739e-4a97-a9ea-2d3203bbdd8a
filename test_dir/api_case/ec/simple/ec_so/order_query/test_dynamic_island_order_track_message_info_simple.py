# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_so.order_query.dynamic_island_order_track_message_info import DynamicIslandOrderTrackMessageInfo
from test_dir.api.ec.ec_so.order_query.list_today_order import ListTodayOrder


class TestDynamicIslandOrderTrackMessageInfoSimple(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_dynamic_island_order_track_message_info(self, ec_login_header):
        """ # test_dynamic_island_order_track_message_info """
        # 获取今日订单id
        today_order = ListTodayOrder().list_today_order(headers=ec_login_header)
        if today_order["object"]["today_orders_num"] > 0:
            order_id = today_order["object"]["myOrders"][0]["id"]
            DynamicIslandOrderTrackMessageInfo().dynamic_island_order_track_message_info(ec_login_header, order_id)
        else:
            log.info("没有订单，请先下单")
        # 断言
        assert self.response["result"] is True


