# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_so.order_query.get_order_status import GetOrderStatus
from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder



class TestGetOrderStatusInfoSimple(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_get_order_status_v1(self, *args, ec_login_header):
        """ # test_get_order_status_v1 """
        # 获取已发货订单，拿到order_id
        list = ListMyOrder().list_my_order_v1(ec_login_header, "all")
        if list["object"]["total"] > 0:
            order_id = list["object"]["myOrders"][0]["id"]
            if order_id is None:
                order_id = args[0]["listmyorder"]["order_id"]
                GetOrderStatus().get_order_status_v1(ec_login_header, order_id)
            else:
                GetOrderStatus().get_order_status_v1(ec_login_header, order_id)
            # 断言
            assert self.response["result"] is True
        else:
            log.info("没有订单，请先下单")
            # 断言
            assert self.response["result"] is True

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product')
    def test_get_order_status_v2(self, *args, ec_login_header):
        """ # test_get_order_status_v2 """
        # 获取已发货订单，拿到order_id
        list = ListMyOrder().list_my_order_v1(ec_login_header, "all")
        if list["object"]["total"] > 0:
            order_id = list["object"]["myOrders"][0]["id"]
            if order_id is None:
                order_id = args[0]["listmyorder"]["order_id"]
                GetOrderStatus().get_order_status_v2(ec_login_header, order_id)
            else:
                GetOrderStatus().get_order_status_v2(ec_login_header, order_id)
            # 断言
            assert self.response["result"] is True
        else:
            log.info("没有订单，请先下单")
            # 断言
            assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
