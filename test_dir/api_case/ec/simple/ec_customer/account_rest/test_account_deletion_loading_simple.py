# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/8/1
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from test_dir.api.ec.ec_customer.account_rest.account_deletion import AccountDeletion
from test_dir.api.ec.ec_customer.login_rest.email_login import EmailLogin
from test_data.ec.simple.common import Header


class TestAccountDeletionLoading(weeeTest.TestCase):

    @weeeTest.params.data([True])
    @weeeTest.mark.list('Social')
    def test_account_deletion_loading(self, expected_result):
        """删除账号landing页面接口"""
        # 特殊登陆账号，不迁往线上
        adl_headers = Header().login_header(email='<EMAIL>', password='1')
        EmailLogin().email_login(headers=adl_headers, email='<EMAIL>', password='1')
        AccountDeletion().accout_deletion_loading(headers=adl_headers)
        dict_data = self.response
        case_result = dict_data["object"]
        if expected_result:
            assert case_result is not None
        else:
            assert case_result is None
        return case_result


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)
