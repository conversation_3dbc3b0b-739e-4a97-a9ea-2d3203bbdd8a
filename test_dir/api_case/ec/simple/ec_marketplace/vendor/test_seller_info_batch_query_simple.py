# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_desc.py
@Description    :
@CreateTime     :  2023/7/20 19:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 19:06
"""

import weeeTest
from test_dir.api.ec.ec_marketplace.sellerinfo.seller_info_batch_query import VendorInfoBatchQuery


class TestVendorBatchQuerySimple(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_marketplace_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_seller_batch_query(self, *args, ec_charlie_header):
        """
                #test_vendro_batch_query 批量查询商家信息
        """

        print(args[0]["seller_info"]["vendor_id"])
        VendorInfoBatchQuery().vendor_batch_query(headers=ec_charlie_header, vendor_list=args[0]["seller_info"]["vendor_list"])
        # 断言
        assert self.response["result"] is True
