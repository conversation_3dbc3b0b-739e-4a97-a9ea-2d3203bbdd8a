import logging

import pytest
import weeeTest


from test_dir.api.ec.ec_marketplace.coupon_apis.plan_id_coupon import CouponsSellers
from test_dir.api.ec.ec_marketplace.global_puls_interfaces.get_global_welcome_coupons import GetGlobalWelcomeCoupons
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from weeeTest import log


class TestCouponsSellersSimple(weeeTest.TestCase):


    @pytest.fixture(scope='class')
    def setup(self, ec_login_header):
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        """landing页面的优惠券列表 fou you"""
        coupon = GetGlobalWelcomeCoupons().get_global_welcome_coupons(headers=ec_login_header, zipcode=porder["zipcode"],
                                                                      source_key=None, tab_key="all")
        yield porder, coupon
        print('>>>>>coupon', coupon)

    def test_gcoupons_sellers(self, setup, ec_login_header):
        """#mkpl新人优惠券列表信息"""
        # porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        # # """landing页面的优惠券列表 fou you"""
        # # coupon = GetGlobalWelcomeCoupons().get_global_welcome_coupons(headers=headers, zipcode=porder["zipcode"],
        # #                                                               source_key=None, tab_key="all")
        # coupon = TestGetCoupon().test_get_coupon()
        # print(coupon)

        if setup[1]["object"] is None:
            log.info(f'没有可领取的coupon')
        else:
            # """领取优惠券"""
            # CouponsSellers().coupons_sellers(headers=headers, vender_id=coupon["coupons"][0]["seller_id"],
            #                                  plan_id=coupon["coupons"][0]["plan_id"])
            vender_id = setup[1]["object"]["coupons"][0]["seller_id"]
            plan_id = setup[1]["object"]["coupons"][0]["plan_id"]
            CouponsSellers().coupons_sellers(headers=ec_login_header, vender_id=vender_id, plan_id=plan_id)

            # """landing页面的优惠券列表 claimed，已领取的优惠券"""
            claimed = GetGlobalWelcomeCoupons().get_global_welcome_coupons(headers=ec_login_header,
                                                                           zipcode=setup[0]["zipcode"],
                                                                           source_key=None, tab_key="claimed")
            claimed_vender_id = claimed["object"]["coupons"][0]["seller_id"]
            # if claimed["coupons"][0]["seller_id"] == coupon["coupons"][0]["seller_id"]:
            # if claimed["coupons"][0]["seller_id"] == vender_id:
            #     print("领取成功")

            # assert self.response["result"] is True
            assert vender_id == claimed_vender_id, f'vender_id:{vender_id},claimed_vender_id:{claimed_vender_id}'
