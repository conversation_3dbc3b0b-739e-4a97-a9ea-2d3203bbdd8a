"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_points_order.py
@Description    :  
@CreateTime     :  2023/11/3 13:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/3 13:53
"""
import weeeTest

from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.preorder.payment_category import PaymentCategory


class TestPointsOrder(weeeTest.TestCase):
    """订单-购买积分订单"""

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('tb1')
    def test_points_order(self, *args, ec_zhuli_header):
        # 结算暂时不迁到线上
        # headers = Header().login_header("<EMAIL>", "1234qwer")
        # 获取用户的preorder
        PrepareCheckout().point_checkout_pre(headers=ec_zhuli_header, product_type=args[0]["cart"]["product_type"])
        profile_id = PaymentCategory().braintree_profiles(headers=ec_zhuli_header)["object"][0]['profile_id']
        # 使用信用卡支付
        PaymentCategory().payment_category(headers=ec_zhuli_header, payment_category=args[0]["order"]["payment_category"],
                                           profile_id=profile_id, points=False)
        # 结算
        print("虚拟订单号",
              Checkout().point_checkout(headers=ec_zhuli_header, product_type=args[0]["cart"]["product_type"])["object"][
                  "order_id"])


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
