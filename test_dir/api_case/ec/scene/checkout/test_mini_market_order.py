"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_mini_market_order.py
@Description    :  
@CreateTime     :  2023/11/3 15:05
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/3 15:05
"""
import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_item.ds_collection_rest.ds_collection_mini_market_detail import DsCollectionMiniMarketDetail
from test_dir.api.ec.ec_so.order.cancel_order import CancelOrder
from test_dir.api.ec.ec_so.order.checkout import Checkout
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.preorder.payment_category import PaymentCategory


class TestMinimarketOrder(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list( 'tb1')
    def test_mini_market_order(self, *args, ec_zhuli_header):
        # 结算暂时不迁到线上
        """订单-使用积分购买minimarket订单"""
        products = DsCollectionMiniMarketDetail().ds_collection_mini_market_detail(ec_zhuli_header, key="61f6g",
                                                                                   category_num=None)
        print("ssss",products)
        itmes = jmespath(products, "object.products")
        product_list = []
        for item in itmes:
            # 通过肉分类获取商品状态不是售罄的商品进行结算
            if item["sold_status"] != "sold_out":
                product_list.append(item["id"])
        print("商品id", product_list)
        if product_list is not None:

            minipre = PrepareCheckout().mini_market_checkout_pre(headers=ec_zhuli_header,
                                                                 product_id=product_list[0])
            points = minipre["object"]["points_price"] * 100
            # profile_id = PaymentCategory().braintree_profiles(headers=RequestHeader.ec_zhuli_header)["object"][0]['profile_id']
            # 使用积分支付
            PaymentCategory().payment_category(headers=ec_zhuli_header,
                                               payment_category=args[0]["order"]["payment_category"],
                                               points=True)
            # 结算

            order_id = \
                Checkout().minimarket_checkout(headers=ec_zhuli_header, points=points, product_id=product_list[0])[
                    "object"][
                    "order_id"]
        else:
            minipre = PrepareCheckout().mini_market_checkout_pre(headers=ec_zhuli_header,
                                                                 product_id=args[0]["cart"]["mini_market_product_id"])
            points = minipre["object"]["points_price"] * 100
            # profile_id = PaymentCategory().braintree_profiles(headers=RequestHeader.ec_zhuli_header)["object"][0]['profile_id']
            # 使用积分支付
            PaymentCategory().payment_category(headers=ec_zhuli_header,
                                               payment_category=args[0]["order"]["payment_category"],
                                               points=True)
            # 结算

            order_id = \
                Checkout().minimarket_checkout(headers=ec_zhuli_header, points=points, product_id=product_list[0])[
                    "object"][
                    "order_id"]
        print("minimarket订单号", order_id)

        # 取消mini market订单
        print("取消结果", CancelOrder().cancel_order(headers=ec_zhuli_header, order_id=order_id))


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
