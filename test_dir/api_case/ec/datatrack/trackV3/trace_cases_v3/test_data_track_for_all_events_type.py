import ast

import allure
import asyncio
import pytest
import weeeTest
from weeeTest import log

from test_dir.api_case.ec.datatrack.base_case import BaseCase
from test_dir.api_case.ec.datatrack.trackV3.case_generator.specific_track_case import T2ClickActionCase, generate_case_data, T2CartActionCase, PageView, T2ProdImpCase, T2CartImpCase
from test_dir.api_case.ec.datatrack.trackV3.read_excel.read_excel_file import ReadCsv
from allure import step
from pytest_assume.plugin import assume


class TestDataTrackForAllEvents(weeeTest.TestCase, BaseCase):
    def non_test_get_all_clickhouse_data(self, clickhouse_client, time_range):
        sql_final = f""" select * from weee_data.data_tracking_all where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' """
        i = 0
        with clickhouse_client.query_row_block_stream(sql_final) as stream:
            for block in stream:
                # for row in block:
                i += 1
                log.info(i)

    r_t2_cart_imp = ReadCsv("t2_cart_imp.csv").read()
    t2_cart_imp = T2CartImpCase(r_t2_cart_imp)
    t2_cart_imp_all_data = generate_case_data(t2_cart_imp).get("all")
    print(1)

    @allure.suite("t2_cart_imp")
    @pytest.mark.parametrize("case_name, sql, platform_page_key, assertion, owner, case_id", t2_cart_imp_all_data)
    @pytest.mark.t2_cart_imp
    def test_t2_cart_imp_v3(self, case_name, sql, platform_page_key, assertion, owner, case_id, clickhouse_client, time_range, tb1_conn):
        # 只能用for循环来处理platform_page_key， 这个参数不能做参数化
        if platform_page_key:
            _date = time_range.get("begin").split(" ")[0]
            for item in platform_page_key:
                sql_final = sql.format(time_range.get("begin"), time_range.get("end"), item[0], item[1])
                print("sql===>", sql_final)
                result_rows = self.query_track(client=clickhouse_client, sql=sql_final).result_rows

                self._save_to_track_table(_tb1_conn=tb1_conn, _platform=item[0], _case_id=case_id, _count_date=_date, _counts=len(result_rows), _case_name=case_name)

                with open(f"{_date}_t2_cart_imp.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(case_name + " 平台为: " + item[0] + " " + " t2_cart_imp" + " " + str(len(result_rows)) + "\n")

                with assume, step(f"检查-{case_name}-埋点数据， platform={item[0]}, page_key={item[1]}, owner={owner}"):
                    assert result_rows, f"未检查到t2_cart_imp埋点，case_name={case_name}, platform={item[0]}, page_key={item[1]}, sql={sql_final}, owner={owner}"

    r_t2_prod_imp = ReadCsv("t2_prod_imp.csv").read()
    t2_prod_imp = T2ProdImpCase(r_t2_prod_imp)
    t2_prod_imp_all_data = generate_case_data(t2_prod_imp).get("all")
    print(1)
    @allure.suite("t2_prod_imp")
    @pytest.mark.parametrize("case_name, sql, platform_page_key, assertion, owner, case_id", t2_prod_imp_all_data)
    @pytest.mark.t2_prod_imp
    def test_t2_prod_imp_v3(self, case_name, sql, platform_page_key, assertion, owner, case_id, clickhouse_client, time_range, tb1_conn):
        # 只能用for循环来处理platform_page_key， 这个参数不能做参数化
        if platform_page_key:
            _date = time_range.get("begin").split(" ")[0]
            for item in platform_page_key:
                sql_final = sql.format(time_range.get("begin"), time_range.get("end"), item[0], item[1])
                print("sql===>", sql_final)
                result_rows = self.query_track(client=clickhouse_client, sql=sql_final).result_rows

                self._save_to_track_table(_tb1_conn=tb1_conn, _platform=item[0], _case_id=case_id, _count_date=_date, _counts=len(result_rows), _case_name=case_name)

                with open(f"{_date}_t2_prod_imp.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(case_name + " 平台为: " + item[0] + " " + " t2_prod_imp" + " " + str(len(result_rows)) + "\n")

                with assume, step(f"检查-{case_name}-埋点数据， platform={item[0]}, page_key={item[1]}, owner={owner}"):
                    assert result_rows, f"未检查到t2_prod_imp埋点，case_name={case_name}, platform={item[0]}, page_key={item[1]}, sql={sql_final}, owner={owner}"

    r_t2_click_action = ReadCsv("t2_click_action.csv").read()
    t2_click = T2ClickActionCase(r_t2_click_action)
    t2_click_all_data = generate_case_data(t2_click).get("all")
    print(1)
    @allure.suite("t2_click_action")
    @pytest.mark.parametrize("case_name, sql, platform_page_key, assertion, owner, case_id", t2_click_all_data)
    @pytest.mark.t2_click_action
    def test_t2_click_action_v3(self, case_name, sql, platform_page_key, assertion, owner, case_id, clickhouse_client, time_range, tb1_conn):
        # 只能用for循环来处理platform_page_key， 这个参数不能做参数化
        if platform_page_key:
            _date = time_range.get("begin").split(" ")[0]
            for item in platform_page_key:
                sql_final = sql.format(time_range.get("begin"), time_range.get("end"), item[0], item[1])
                print("sql===>", sql_final)
                result_rows = self.query_track(client=clickhouse_client, sql=sql_final).result_rows

                self._save_to_track_table(_tb1_conn=tb1_conn, _platform=item[0], _case_id=case_id, _count_date=_date, _counts=len(result_rows), _case_name=case_name)

                with open(f"{_date}_t2_click_action.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(case_name + " 平台为: " + item[0] + " " + " t2_click_action" + " " + str(len(result_rows)) + "\n")

                with assume, step(f"检查-{case_name}-埋点数据， platform={item[0]}, page_key={item[1]}, owner={owner}"):
                    assert result_rows, f"未检查到t2_click埋点，case_name={case_name}, platform={item[0]}, page_key={item[1]}, sql={sql_final}, owner={owner}"



    r_t2_cart_action = ReadCsv("t2_cart_action.csv").read()
    t2_cart_action = T2CartActionCase(r_t2_cart_action)
    t2_cart_action_all_data = generate_case_data(t2_cart_action).get("all")

    @allure.suite("t2_cart_action")
    @pytest.mark.parametrize("case_name, sql, assertion, platform_page_key, owner, case_id", t2_cart_action_all_data)
    @pytest.mark.t2_cart_action
    def test_t2_cart_action_v3(self, case_name, sql, assertion, platform_page_key, owner, case_id, clickhouse_client, time_range, tb1_conn):
        # 只能用for循环来处理platform_page_key， 这个参数不能做参数化
        if platform_page_key:
            _date = time_range.get("begin").split(" ")[0]
            for item in platform_page_key:
                sql_final = sql.format(time_range.get("begin"), time_range.get("end"), item[0], item[1])
                print("sql===>", sql_final)
                result_rows = self.query_track(client=clickhouse_client, sql=sql_final).result_rows

                self._save_to_track_table(_tb1_conn=tb1_conn, _platform=item[0], _case_id=case_id, _count_date=_date, _counts=len(result_rows), _case_name=case_name)

                with open(f"{_date}_t2_cart_action.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(case_name + " 平台为: " + item[0] + " " + " t2_cart_action" + " " + str(len(result_rows)) + "\n")

                with assume, step(f"检查-{case_name}-埋点数据， platform={item[0]}, page_key={item[1]}, owner={owner}"):
                    assert result_rows, f"未检查到t2_action埋点，case_name={case_name}, platform={item[0]}, page_key={item[1]}, sql={sql_final}, owner={owner}"



    r_page_view = ReadCsv("page_view.csv").read()
    page_view = PageView(r_page_view)
    page_view_all_data = generate_case_data(page_view).get("all")

    @allure.suite("page_view")
    @pytest.mark.parametrize("case_name, sql, assertion, platform_page_key, owner, case_id", page_view_all_data)
    @pytest.mark.page_view
    def test_page_view_v3(self, case_name, sql, assertion, platform_page_key, owner, case_id, clickhouse_client, time_range, tb1_conn):
        # 只能用for循环来处理platform_page_key， 这个参数不能做参数化
        if platform_page_key:
            _date = time_range.get("begin").split(" ")[0]
            for item in platform_page_key:
                sql_final = sql.format(time_range.get("begin"), time_range.get("end"), item[0], item[1])
                print(f"{case_name}的执行sql为:===>", sql_final)
                result_rows = self.query_track(client=clickhouse_client, sql=sql_final).result_rows

                self._save_to_track_table(_tb1_conn=tb1_conn, _platform=item[0], _case_id=case_id, _count_date=_date, _counts=len(result_rows), _case_name=case_name)

                with open(f"{_date}_page_view.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(case_name + " 平台为: " + item[0] + " " + " page_view" + " " + str(len(result_rows)) + "\n")

                with assume, step(f"检查-{case_name}-埋点数据， platform={item[0]}, page_key={item[1]}, owner={owner}"):
                    assert result_rows, f"未检查到埋点，case_name={case_name}, platform={item[0]}, page_key={item[1]}, sql={sql_final}, owner={owner}"

    def _save_to_track_table(self, _tb1_conn, _platform, _case_id, _count_date, _counts, _case_name):
        _sql = f""" select * from autotest_result.data_track_record where case_id='{_case_id}' and count_date='{_count_date}' """
        result = asyncio.run(_tb1_conn.execute_query(sql_str=_sql))
        __sql = ""
        # 如果记录已存在，update，否则insert
        if result != '[]' and result != '' and result != []:
            if _platform == "iOS":
                __sql = f""" update autotest_result.data_track_record set ios_counts={_counts} where case_id='{_case_id}' and count_date='{_count_date}' """
            elif _platform == "Android":
                __sql = f""" update autotest_result.data_track_record set android_counts={_counts} where case_id='{_case_id}' and count_date='{_count_date}' """
            elif _platform == "DWeb":
                __sql = f""" update autotest_result.data_track_record set dweb_counts={_counts} where case_id='{_case_id}' and count_date='{_count_date}' """
            elif _platform == "MWeb":
                __sql = f""" update autotest_result.data_track_record set mweb_counts={_counts} where case_id='{_case_id}' and count_date='{_count_date}' """

            if __sql:
                asyncio.run(_tb1_conn.execute_update(sql_str=__sql))
            else:
                raise Exception(f"update的__sql的值不正确：__sql={__sql}")
        else:
            # 判断平台，update相应的字段
            if _platform == "iOS":
                __sql = f""" insert into autotest_result.data_track_record (case_id, case_name, ios_counts, android_counts, dweb_counts, mweb_counts, count_date) values ('{_case_id}', '{_case_name}', {_counts},null,null,null, '{_count_date}') """
            elif _platform == "Android":
                __sql = f""" insert into autotest_result.data_track_record (case_id, case_name, ios_counts, android_counts, dweb_counts, mweb_counts, count_date) values ('{_case_id}', '{_case_name}', null,{_counts},null,null, '{_count_date}') """
            elif _platform == "DWeb":
                __sql = f""" insert into autotest_result.data_track_record (case_id, case_name, ios_counts, android_counts, dweb_counts, mweb_counts, count_date) values ('{_case_id}', '{_case_name}', null,null,{_counts},null, '{_count_date}') """
            elif _platform == "MWeb":
                __sql = f""" insert into autotest_result.data_track_record (case_id, case_name, ios_counts, android_counts, dweb_counts, mweb_counts, count_date) values ('{_case_id}', '{_case_name}', null,null,null,{_counts}, '{_count_date}') """

            if __sql:
                asyncio.run(_tb1_conn.execute_update(sql_str=__sql))
            else:
                raise Exception(f"insert的__sql的值不正确：__sql={__sql}")




