# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from datetime import datetime, timedelta

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesImageSearchLogs(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_image_search_logs(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-查询图片搜索日志页面数据（Search Type默认是All）"""
        search_log_list = CentralIm().search_log_list(headers=sales_header)
        assert len(search_log_list["object"]["data"]) > 0, f'查询图片搜索日志页面数据异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_log_list_search_type(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试search_type字段（image和upc_code）"""
        # 定义不同的search_type测试数据
        search_types = [
            {"value": "image", "name": "Image"},
            {"value": "upc_code", "name": "UPC Code"}
        ]

        # 循环测试每个search_type
        for search_type_data in search_types:
            search_type_value = search_type_data["value"]
            search_type_name = search_type_data["name"]

            search_log_list = CentralIm().search_log_list(
                headers=sales_header,
                search_type=search_type_value,
                startColumn=0,
                pageSize=10
            )
            assert search_log_list["result"] is True, f'search_type字段({search_type_name})测试失败{search_log_list}'
            assert "data" in search_log_list["object"], f'search_type({search_type_name})返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_log_list_user_id(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试user_id字段"""
        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            user_id="8903000",
            startColumn=0,
            pageSize=10
        )
        assert search_log_list["result"] is True, f'user_id字段测试失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_search_log_list_time_range(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-图片搜索日志-测试start_time和end_time时间区间查询"""
        # 设置时间区间：当前时间减7天到当前时间
        start_time = int((datetime.now() - timedelta(days=7)).timestamp())
        end_time = int(datetime.now().timestamp())

        search_log_list = CentralIm().search_log_list(
            headers=sales_header,
            start_time=start_time,
            end_time=end_time,
            startColumn=0,
            pageSize=10
        )
        assert search_log_list["result"] is True, f'时间区间查询测试失败{search_log_list}'
        assert "data" in search_log_list["object"], f'返回数据格式异常{search_log_list}'


